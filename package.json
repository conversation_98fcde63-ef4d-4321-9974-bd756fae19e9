{"name": "factcheck-platform", "version": "1.0.0", "description": "FactCheck Anti-Fraud Platform - Microservices Architecture", "private": true, "scripts": {"start": "echo 🚀 Starting Full Stack Application... && npm run start:full", "start:full": "concurrently --kill-others-on-fail \"npm run start:services\" \"npm run start:client:delayed\"", "start:services": "concurrently --kill-others-on-fail \"npm run start:auth\" \"npm run start:api-gateway\" \"npm run start:admin\" \"npm run start:chat\" \"npm run start:community\" \"npm run start:link\" \"npm run start:news\"", "start:client": "cd client && npm start", "start:client:delayed": "echo ⏳ Waiting for services to start... && sleep 20 && npm run start:client", "start:auth": "cd services/auth-service && npm start", "start:api-gateway": "cd services/api-gateway && npm start", "start:admin": "cd services/admin-service && npm start", "start:chat": "cd services/chat-service && npm start", "start:community": "cd services/community-service && npm start", "start:link": "cd services/link-service && npm start", "start:news": "cd services/news-service && npm start", "dev": "echo 🔧 Starting development environment... && npm run dev:full", "dev:full": "concurrently --kill-others-on-fail \"npm run dev:services\" \"npm run dev:client:delayed\"", "dev:services": "concurrently --kill-others-on-fail \"npm run dev:auth\" \"npm run dev:api-gateway\" \"npm run dev:admin\" \"npm run dev:chat\" \"npm run dev:community\" \"npm run dev:link\" \"npm run dev:news\"", "dev:client": "cd client && npm run start", "dev:client:delayed": "echo ⏳ Waiting for services to start... && sleep 20 && npm run dev:client", "dev:auth": "cd services/auth-service && npm run dev", "dev:api-gateway": "cd services/api-gateway && npm run dev", "dev:admin": "cd services/admin-service && npm run dev", "dev:chat": "cd services/chat-service && npm run dev", "dev:community": "cd services/community-service && npm run dev", "dev:link": "cd services/link-service && npm run dev", "dev:news": "cd services/news-service && npm run dev", "install:all": "npm install && cd client && npm install && cd ../services/auth-service && npm install && cd ../api-gateway && npm install && cd ../admin-service && npm install && cd ../chat-service && npm install && cd ../community-service && npm install && cd ../link-service && npm install && cd ../news-service && npm install", "deploy:local": "./scripts/deploy-local.sh", "deploy:docker": "./scripts/deploy-docker.sh", "deploy:k8s": "./scripts/deploy-k8s.sh", "stop:local": "./scripts/stop-local.sh", "stop:docker": "./scripts/stop-docker.sh", "stop:k8s": "./scripts/stop-k8s.sh", "health": "curl -s http://localhost:8080/services/status || echo 'Services not running'", "logs:docker": "docker-compose logs -f", "logs:k8s": "kubectl logs -f deployment/api-gateway -n anti-fraud-platform"}, "dependencies": {"concurrently": "^8.2.2", "all": "^0.0.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["client", "services/*"]}