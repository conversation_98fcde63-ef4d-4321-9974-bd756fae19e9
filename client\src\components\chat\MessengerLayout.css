/* Messenger Layout CSS - Desktop First */
.chat-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  height: 100vh;
  width: 100vw;
  background: white;
}

.messenger-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: white;
}

.messenger-header {
  flex-shrink: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  z-index: 10;
}

.messenger-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 0;
  background: white;
}

/* Sidebar Styles - Desktop Layout */
.messenger-sidebar {
  width: 360px;
  min-width: 360px;
  max-width: 360px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.messenger-sidebar.collapsed {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
}

.messenger-sidebar.open {
  display: flex;
}

/* Chat Area Styles - Desktop Layout */
.messenger-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  min-height: 0;
  width: calc(100% - 360px);
}

/* Messages Area */
.messenger-messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: white;
}

.messenger-bubble-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

/* Message Bubbles */
.message-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 8px;
}

.message-container.user {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.message-container.bot {
  flex-direction: row;
  justify-content: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  position: relative;
}

.message-bubble.user {
  background: #0084ff;
  color: white;
  border-bottom-right-radius: 4px;
  margin-left: auto;
}

.message-bubble.bot {
  background: #f1f3f4;
  color: #1c1e21;
  border-bottom-left-radius: 4px;
  margin-right: auto;
}

.message-timestamp {
  font-size: 11px;
  color: #65676b;
  text-align: center;
  margin-top: 4px;
}

/* Input Area */
.messenger-input-container {
  flex-shrink: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.messenger-input-box {
  flex: 1;
  min-height: 36px;
  max-height: 120px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 18px;
  resize: none;
  outline: none;
  font-size: 14px;
  line-height: 1.4;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.messenger-input-box:focus {
  border-color: #0084ff;
  background: white;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1);
}

.messenger-attachment-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f8fafc;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #65676b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.messenger-attachment-button:hover {
  background: #e4e6ea;
  color: #1c1e21;
}

.messenger-send-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #0084ff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.messenger-send-button:hover {
  background: #0066cc;
  transform: scale(1.05);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 8px;
}

.typing-dots {
  background: #f1f3f4;
  border-radius: 18px;
  padding: 12px 16px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #65676b;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Desktop First - Default Layout */
@media (min-width: 1025px) {
  .messenger-sidebar {
    position: relative;
    transform: none;
    display: flex;
  }

  .messenger-chat-area {
    width: calc(100% - 360px);
  }

  .messenger-sidebar.collapsed + .messenger-chat-area {
    width: calc(100% - 80px);
  }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .messenger-sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    height: calc(100vh - 60px);
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .messenger-sidebar.open {
    transform: translateX(0);
  }

  .messenger-chat-area {
    width: 100%;
  }
}

/* Dark Mode */
.dark .messenger-header {
  background: #1f2937;
  border-color: #374151;
}

.dark .messenger-sidebar {
  background: #1f2937;
  border-color: #374151;
}

.dark .messenger-chat-area {
  background: #111827;
}

.dark .messenger-messages-area {
  background: #1f2937;
}

.dark .message-bubble.bot {
  background: #374151;
  color: #f9fafb;
}

.dark .messenger-input-container {
  background: #1f2937;
  border-color: #374151;
}

.dark .messenger-input-box {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .messenger-input-box:focus {
  background: #4b5563;
  border-color: #0084ff;
}

.dark .messenger-attachment-button {
  background: #374151;
  color: #9ca3af;
}

.dark .messenger-attachment-button:hover {
  background: #4b5563;
  color: #f9fafb;
}
